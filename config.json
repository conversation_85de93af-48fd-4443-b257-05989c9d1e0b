{"listen_addr": "localhost:8443", "require_client_cert": true, "certs": {"host_cert": "certs\\host.crt", "host_key": "certs\\host.key", "ca_cert": "certs\\ca.crt"}, "hmac": {"secret": "mysecret123", "secret_file": "certs\\hmac_secret.txt"}, "mqtt": {"host": "localhost", "port": 1883, "client_id": "go-systemsupport", "username": "admin", "password": "123", "TopicSupport": "SystemSupport", "StatusTopic": "SystemSupportStatus", "tls": {"enable": false, "insecure_skip_verify": false}}, "influx": {"enable": true, "url": "http://localhost:8086", "token": "yWZo5tXbdjsTp5vbEGoUFCuJ-xvDGByL30cdnDpqiV56GvVwi_pCbmlbkB-wijZKlMM2aly5ZQ_U4mOCgVTrQA==", "org": "ReportSystem", "default_bucket": "Dao1"}}