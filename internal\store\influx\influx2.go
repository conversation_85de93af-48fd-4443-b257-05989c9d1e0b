package influx

import (
	"context"
	"fmt"

	influxdb2 "github.com/influxdata/influxdb-client-go/v2"
	"github.com/influxdata/influxdb-client-go/v2/api"
)

type ClientV2 struct {
	client   influxdb2.Client
	queryAPI api.QueryAPI
	org      string
}

type V2Config struct {
	URL   string
	Token string
	Org   string
	// Nếu dùng self-signed cert, tu<PERSON> chọn InsecureSkipVerify có thể set qua client Options.
	// Đơn giản hoá ví dụ: dùng NewClient(URL, Token) mặc định.
}

func NewV2(cfg V2Config) *ClientV2 {
	c := influxdb2.NewClient(cfg.URL, cfg.Token)
	return &ClientV2{
		client:   c,
		queryAPI: c.QueryAPI(cfg.Org),
		org:      cfg.Org,
	}
}

func (c *ClientV2) Query(ctx context.Context, org, flux string) ([]map[string]interface{}, error) {
	if org == "" {
		org = c.org
	}
	q := c.client.QueryAPI(org)
	res, err := q.Query(ctx, flux)
	if err != nil {
		return nil, err
	}
	defer res.Close()

	var out []map[string]interface{}
	for res.Next() {
		// Values() trả về map[string]interface{} của record hiện tại
		row := map[string]interface{}{}
		for k, v := range res.Record().Values() {
			row[k] = v
		}
		out = append(out, row)
	}
	if res.Err() != nil {
		return nil, fmt.Errorf("flux result error: %w", res.Err())
	}
	return out, nil
}

func (c *ClientV2) Close() {
	if c.client != nil {
		c.client.Close()
	}
}
