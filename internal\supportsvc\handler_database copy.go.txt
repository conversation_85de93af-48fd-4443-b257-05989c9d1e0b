package supportsvc

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"

	"Golang/SystemSupport/internal/store/influx"
)

type DatabaseHandler struct {
	MQTT          mqtt.Client
	QoS           byte
	Retain        bool
	DefaultOrg    string
	DefaultBucket string
	Querier       influx.Querier // interface chung cho Influx
}

func (h *DatabaseHandler) Kind() string { return "database" }

// DBRequest là payload cụ thể của type "database"
type DBRequest struct {
	Org        string `json:"org,omitempty"`
	Bucket     string `json:"bucket,omitempty"`
	Query      string `json:"query"`                 // Flux query
	Output     string `json:"output"`                // topic publish kết quả
	TimeFormat string `json:"time_format,omitempty"` // tuỳ chọn: format thời gian khi render
}

func (h *DatabaseHandler) Handle(ctx context.Context, raw json.RawMessage) error {
	var req DBRequest
	if err := json.Unmarshal(raw, &req); err != nil {
		return fmt.Errorf("bad database payload: %w", err)
	}
	if req.Query == "" || req.Output == "" {
		return errors.New("missing query or output topic")
	}
	org := req.Org
	if org == "" {
		org = h.DefaultOrg
	}
	// Với Flux bạn không bắt buộc truyền bucket riêng nếu câu query đã “from(bucket: …)”.
	// DefaultBucket hữu ích khi bạn muốn build query động. Ở ví dụ này ta giữ tuỳ chọn.
	bucket := req.Bucket
	if bucket == "" {
		bucket = h.DefaultBucket
	}

	start := time.Now()
	rows, err := h.Querier.Query(ctx, org, req.Query)
	dur := time.Since(start)
	if err != nil {
		log.Printf("[db-handler] query error: %v", err)
		// tuỳ chọn: publish lỗi về output
		errPayload := map[string]any{
			"error": err.Error(),
		}
		h.publish(req.Output, errPayload)
		return err
	}

	// Chuẩn hoá kết quả: []map[string]any -> JSON
	resp := map[string]any{
		"org":        org,
		"bucket":     bucket,
		"rowCount":   len(rows),
		"durationMs": dur.Milliseconds(),
		"data":       len(rows), // đã là []map[string]any
	}
	return h.publish(req.Output, resp)
}

func (h *DatabaseHandler) publish(topic string, v any) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}
	tok := h.MQTT.Publish(topic, h.QoS, h.Retain, b)
	if ok := tok.WaitTimeout(5 * time.Second); !ok {
		return tok.Error()
	}
	return tok.Error()
}
