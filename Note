
go mod init example.com/hello-server
go run .
go build -o server.exe

go mod tidy



# 2) T<PERSON>i lib MQTT
go get github.com/eclipse/paho.mqtt.golang@v1   


.
├─ cmd/
│  └─ server/
│     └─ main.go
├─ internal/
│  ├─ config/
│  │  └─ config.go
│  ├─ httpserver/
│  │  └─ httpserver.go
│  ├─ mqttclient/
│  │  └─ mqttclient.go
│  ├─ sign/
│  │  └─ sign.go
│  └─ sysinfo/
│     └─ sysinfo.go
├─ config.json
└─ go.mod
