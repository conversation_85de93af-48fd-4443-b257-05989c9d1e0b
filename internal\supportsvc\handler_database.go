package supportsvc

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"strings"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

// ---- Querier interface (khớp với phần Influx của bạn) ----
// rows kỳ vọng: []map[string]any sau khi query Flux
type Querier interface {
	Query(ctx context.Context, org, bucket, flux string) ([]map[string]any, error)
	Close() error
}

// ---- DatabaseHandler ----

type DatabaseHandler struct {
	MQTT          mqtt.Client
	QoS           byte
	Retain        bool
	DefaultOrg    string
	DefaultBucket string
	Querier       Querier

	// Tùy chọn cho sandbox script
	ScriptTimeout time.Duration // default 2s nếu =0
	MaxRows       int           // cắt nhỏ rows trước khi chạy script (0 = không cắt)
}

// Kind implements Handler
func (h *DatabaseHandler) Kind() string { return "database" }

// DBPayload: request từ MQTT
type DBPayload struct {
	Action        string      `json:"action"` // "query" (mặc định)
	Org           string      `json:"org,omitempty"`
	Bucket        string      `json:"bucket,omitempty"`
	Query         string      `json:"query,omitempty"`         // Flux
	Script        *ScriptSpec `json:"script,omitempty"`        // nếu muốn post-process
	ResponseTopic string      `json:"responseTopic,omitempty"` // hoặc "output" theo bạn
	CorrelationID string      `json:"correlationId,omitempty"`
	// Back-compat: nếu client gửi "output"
	Output string `json:"output,omitempty"`
}

type ScriptSpec struct {
	Lang string `json:"lang"` // "starlark"
	Code string `json:"code"` // yêu cầu định nghĩa hàm run(rows)
}

func (h *DatabaseHandler) Handle(ctx context.Context, raw json.RawMessage) error {
	var p DBPayload
	if err := json.Unmarshal(raw, &p); err != nil {
		return fmt.Errorf("database payload invalid: %w", err)
	}
	if p.Action == "" {
		p.Action = "query"
	}
	if !strings.EqualFold(p.Action, "query") {
		return fmt.Errorf("unsupported action: %s", p.Action)
	}

	org := p.Org
	if org == "" {
		org = h.DefaultOrg
	}
	bucket := p.Bucket
	if bucket == "" {
		bucket = h.DefaultBucket
	}
	if p.Query == "" {
		return errors.New("missing query")
	}

	// 1) chạy Flux
	rows, err := h.Querier.Query(ctx, org, bucket, p.Query)
	if err != nil {
		return h.publishError(p, fmt.Errorf("query error: %w", err))
	}

	// cắt rows nếu cần (tránh script xử lý quá lớn)
	if h.MaxRows > 0 && len(rows) > h.MaxRows {
		rows = rows[:h.MaxRows]
	}

	// 2) nếu có script -> post-process
	var result any = rows
	if p.Script != nil {
		if !strings.EqualFold(p.Script.Lang, "starlark") {
			return h.publishError(p, fmt.Errorf("unsupported script.lang: %s", p.Script.Lang))
		}
		timeout := h.ScriptTimeout
		if timeout <= 0 {
			timeout = 2 * time.Second
		}
		res, sErr := runStarlarkTransform(ctx, p.Script.Code, rows, timeout)
		if sErr != nil {
			return h.publishError(p, fmt.Errorf("script error: %w", sErr))
		}
		result = res
	}

	// 3) publish kết quả
	resp := map[string]any{
		"ok":            true,
		"correlationId": p.CorrelationID,
		"result":        result,
	}
	buf, _ := json.Marshal(resp)

	topic := p.ResponseTopic
	if topic == "" {
		topic = p.Output // back-compat
		if topic == "" {
			topic = "SystemSupport/Response"
		}
	}
	tok := h.MQTT.Publish(topic, h.QoS, h.Retain, buf)
	tok.Wait()
	if err := tok.Error(); err != nil {
		log.Printf("[DatabaseHandler] publish error: %v", err)
		return err
	}
	return nil
}

func (h *DatabaseHandler) publishError(p DBPayload, err error) error {
	resp := map[string]any{
		"ok":            false,
		"correlationId": p.CorrelationID,
		"error":         err.Error(),
	}
	buf, _ := json.Marshal(resp)
	topic := p.ResponseTopic
	if topic == "" {
		topic = p.Output
		if topic == "" {
			topic = "SystemSupport/Response"
		}
	}
	tok := h.MQTT.Publish(topic, h.QoS, h.Retain, buf)
	tok.Wait()
	return err
}
