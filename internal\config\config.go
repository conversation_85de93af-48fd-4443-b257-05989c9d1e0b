package config

import (
	"encoding/json"
	"fmt"
	"os"
)

type Config struct {
	MQTT   *MQTT   `json:"mqtt"`
	Influx *Influx `json:"influx"`
}

type MQTT struct {
	Host         string `json:"host"`
	Port         int    `json:"port"`
	ClientID     string `json:"client_id"`
	Username     string `json:"username"`
	Password     string `json:"password"`
	TopicSupport string `json:"topic_support"`

	// Trạng thái online/offline
	StatusTopic   string `json:"status_topic"`   // mặc định: "SystemSupportStatus"
	StatusOnline  string `json:"status_online"`  // mặc định: "online"
	StatusOffline string `json:"status_offline"` // mặc định: "offline"
	StatusRetain  bool   `json:"status_retain"`  // mặc định: true
	StatusQOS     byte   `json:"status_qos"`     // mặc định: 1

	TLS struct {
		Enable             bool `json:"enable"`
		InsecureSkipVerify bool `json:"insecure_skip_verify"`
	} `json:"tls"`
}

type Influx struct {
	Enable        bool
	URL           string
	Token         string
	Org           string
	DefaultBucket string
	// TLS/Insecure tuỳ nhu cầu; nếu cần, tạo *http.Client có TLSConfig custom rồi
	// truyền vào influxdb2.NewClientWithOptions(...)
}

func Load(path string) (*Config, error) {
	b, err := os.ReadFile(path)
	if err != nil {
		// không coi là fatal — trả về config trống để HTTP vẫn chạy
		return &Config{MQTT: nil}, fmt.Errorf("read %s: %w", path, err)
	}
	var c Config
	if err := json.Unmarshal(b, &c); err != nil {
		return &Config{MQTT: nil}, fmt.Errorf("parse %s: %w", path, err)
	}
	return &c, nil
}
